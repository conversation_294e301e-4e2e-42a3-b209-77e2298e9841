2025-05-30 15:33:33.7195 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-05-30 15:33:33.9060 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-05-30 15:33:33.9080 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-05-30 15:33:33.9080 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-05-30 15:33:33.9080 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-05-30 15:33:33.9087 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-05-30 15:33:33.9087 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-05-30 15:33:33.9087 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-05-30 15:33:33.9110 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-05-30 15:33:33.9116 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:33:58.1298 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:34:27.6540 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:34:52.0259 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:35:16.5855 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
