namespace MaoYouJi
{
  public static partial class GlobalUserManageCompSys
  {
    public static async void RankUserByAttackNum(this GlobalManageComp self)
    {
      // 从数据库获取排名前100的用户
      Query query = new Query();
      query.with(Sort.by(Sort.Direction.DESC, "nowAttack.attackNum"));
      query.limit(100);
      List<User> users = mongoTemplate.find(query, User.class);

    // 获取在线用户
    Collection<UserCacheInfo> onlineUsers = UserBaseProc.userCache.asMap().values();

    // 使用Map来处理重复用户，key为userId，保证同一用户只保留最新的战斗力数据
    Map<String, RankInfo.RankItem> rankMap = new HashMap<>();

    long minAttackVal = 0;
    // 处理数据库中的用户
    for (User user : users) {
      RankInfo.RankItem rankItem = new RankInfo.RankItem();
    rankItem.userId = user.id;
      rankItem.name = user.name;
      rankItem.attackVal = user.nowAttack.attackNum;
      rankItem.skinId = user.nowAttack.nowSkin;
      if (minAttackVal == 0 || minAttackVal > user.nowAttack.attackNum) {
        minAttackVal = user.nowAttack.attackNum;
      }
  rankMap.put(user.id, rankItem);
    }

// 处理在线用户，如果在线用户的战斗力更高，则更新排名信息
for (UserCacheInfo onlineUser : onlineUsers)
{
  User user = onlineUser.user;
  long realAttackVal = user.nowAttack.attackNum;
  if (ActNameEnum.Da_TaoSha != user.activityName || user.backUpAttack == null)
  {
    realAttackVal = user.nowAttack.attackNum;
  }
  else
  {
    realAttackVal = user.backUpAttack.attackNum;
  }
  RankInfo.RankItem existingItem = rankMap.get(user.id);
  if (existingItem != null && existingItem.attackVal < realAttackVal)
  {
    existingItem.attackVal = realAttackVal;
  }
  if (existingItem == null && realAttackVal > minAttackVal)
  {
    RankInfo.RankItem rankItem = new RankInfo.RankItem();
    rankItem.userId = user.id;
    rankItem.name = user.name;
    rankItem.attackVal = realAttackVal;
    rankItem.skinId = user.nowAttack.nowSkin;
    rankMap.put(user.id, rankItem);
  }
}

// 将Map转换为List并按战斗力排序
List<RankInfo.RankItem> rankItems = new ArrayList<>(rankMap.values());
rankItems.sort((a, b)->Long.compare(b.attackVal, a.attackVal));
LogUtil.LevelInfo(10, "rankUserByAttackVal", rankItems.size());
// 只保留前100名
if (rankItems.size() > 100)
{
  rankItems = rankItems.subList(0, 100);
}
for (int i = rankItems.size() - 1; i >= 0; i--)
{
  if (rankItems.get(i).attackVal == 0)
  {
    rankItems.remove(i);
  }
}

// 保存排行榜信息
RankInfo rankInfo = new RankInfo();
rankInfo.id = "8888";
rankInfo.items = rankItems;
rankInfo.updateTime = System.currentTimeMillis();
UserFuncProc.rankInfo.set(rankInfo);
mongoTemplate.save(rankInfo);
    }
  }
}