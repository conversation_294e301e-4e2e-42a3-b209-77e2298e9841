using System.Net;

namespace MaoYouJi
{
  [Invoke((long)SceneType.Global)]
  public class FiberInit_Global : AInvokeHandler<FiberInit, ETTask>
  {
    public override async ETTask Handle(FiberInit fiberInit)
    {
      Scene root = fiberInit.Fiber.Root;
      root.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      root.AddComponent<TimerComponent>();
      root.AddComponent<CoroutineLockComponent>();
      root.AddComponent<ProcessInnerSender>();
      root.AddComponent<MessageSender>();
      root.AddComponent<DBManagerComponent>();
      root.AddComponent<GlobalManageComp>();

      GlobalActorInfo.Instance.GlobalActorId = root.GetActorId();

      await ETTask.CompletedTask;
    }
  }
}